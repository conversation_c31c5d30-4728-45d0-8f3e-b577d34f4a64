#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI对冲基金系统LLM模型代理准确率分析和可视化工具

该脚本用于分析和可视化不同LLM模型在AI对冲基金系统中各代理的准确率表现。
支持从多个reasoning_logs文件夹中读取final_accuracy_report文件，
生成分组柱状图展示不同模型下各代理的准确率对比。

作者: AI Assistant
日期: 2025-07-07
"""

import os
import json
import re
import argparse
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import warnings

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 忽略matplotlib警告
warnings.filterwarnings('ignore', category=UserWarning)

class LLMAccuracyAnalyzer:
    """LLM模型准确率分析器"""
    
    def __init__(self, base_path: str = "reasoning_logs", date_range: Optional[str] = None):
        """
        初始化分析器

        Args:
            base_path: reasoning_logs文件夹的路径
            date_range: 目标日期范围，格式为 "YYYYMMDD-YYYYMMDD"，如果为None则不过滤日期
        """
        self.base_path = Path(base_path)
        self.date_range = date_range
        self.data = {}
        self.models = set()
        self.agents = set()
        self.tickers = set()

    def check_date_range(self, folder_name: str) -> bool:
        """
        检查文件夹名称中的日期范围是否符合要求

        Args:
            folder_name: 文件夹名称

        Returns:
            如果日期范围符合要求或未设置日期过滤则返回True，否则返回False
        """
        # 如果未设置日期范围过滤，则接受所有文件夹
        if not self.date_range:
            return True

        # 从文件夹名称中提取日期范围
        pattern = r'accuracy_tracking_[A-Z]+_(\d{8}-\d{8})_'
        match = re.search(pattern, folder_name)
        if match:
            folder_date_range = match.group(1)
            return folder_date_range == self.date_range

        # 备用模式：experiment_TICKER_DATERANGE_MODEL
        pattern2 = r'experiment_[A-Z]+_(\d{8}-\d{8})_'
        match2 = re.search(pattern2, folder_name)
        if match2:
            folder_date_range = match2.group(1)
            return folder_date_range == self.date_range

        return False

    def extract_model_name(self, folder_name: str) -> Optional[str]:
        """
        从文件夹名称中提取模型名称
        
        Args:
            folder_name: 文件夹名称
            
        Returns:
            提取的模型名称，如果无法提取则返回None
        """
        # 匹配模式：accuracy_tracking_TICKER_DATERANGE_MODEL
        pattern = r'accuracy_tracking_[A-Z]+_\d{8}-\d{8}_(.+)'
        match = re.match(pattern, folder_name)
        if match:
            model_name = match.group(1)
            # 清理模型名称
            model_name = model_name.replace('_', '-')
            return model_name
        
        # 备用模式：experiment_TICKER_DATERANGE_MODEL
        pattern2 = r'experiment_[A-Z]+_\d{8}-\d{8}_(.+)'
        match2 = re.match(pattern2, folder_name)
        if match2:
            model_name = match2.group(1)
            model_name = model_name.replace('_', '-')
            return model_name
            
        return None
    
    def extract_ticker_from_folder(self, folder_name: str) -> Optional[str]:
        """
        从文件夹名称中提取股票代码
        
        Args:
            folder_name: 文件夹名称
            
        Returns:
            提取的股票代码
        """
        pattern = r'accuracy_tracking_([A-Z]+)_\d{8}-\d{8}_'
        match = re.match(pattern, folder_name)
        if match:
            return match.group(1)
            
        pattern2 = r'experiment_([A-Z]+)_\d{8}-\d{8}_'
        match2 = re.match(pattern2, folder_name)
        if match2:
            return match2.group(1)
            
        return None
    
    def load_accuracy_data(self, target_ticker: Optional[str] = None) -> None:
        """
        加载准确率数据
        
        Args:
            target_ticker: 目标股票代码，如果为None则加载所有股票
        """
        print(f"正在扫描 {self.base_path} 文件夹...")
        
        if not self.base_path.exists():
            raise FileNotFoundError(f"路径 {self.base_path} 不存在")
        
        # 遍历所有子文件夹
        for folder in self.base_path.iterdir():
            if not folder.is_dir():
                continue

            # 检查日期范围是否符合要求
            if not self.check_date_range(folder.name):
                continue

            # 提取模型名称和股票代码
            model_name = self.extract_model_name(folder.name)
            ticker = self.extract_ticker_from_folder(folder.name)

            if not model_name or not ticker:
                continue

            # 如果指定了目标股票代码，只处理该股票
            if target_ticker and ticker != target_ticker:
                continue
            
            # 查找final_accuracy_report文件
            report_files = list(folder.glob("final_accuracy_report_experiment_*.json"))
            
            if not report_files:
                print(f"警告: 在 {folder.name} 中未找到final_accuracy_report文件")
                continue
            
            # 使用最新的报告文件
            report_file = max(report_files, key=lambda x: x.stat().st_mtime)
            
            try:
                with open(report_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 处理JSON文件末尾可能存在的注释
                # 找到JSON结束的位置（最后一个}）
                json_end = content.rfind('}')
                if json_end != -1:
                    json_content = content[:json_end + 1]
                else:
                    json_content = content

                data = json.loads(json_content)

                # 提取累积统计数据
                cumulative_stats = data.get('cumulative_stats', {})
                
                # 存储数据
                key = (model_name, ticker)
                self.data[key] = {}
                
                for agent_name, agent_data in cumulative_stats.items():
                    accuracy_rate = agent_data.get('accuracy_rate', 0)
                    self.data[key][agent_name] = accuracy_rate
                    self.agents.add(agent_name)
                
                self.models.add(model_name)
                self.tickers.add(ticker)
                
                print(f"已加载: {model_name} - {ticker} ({len(cumulative_stats)} 个代理)")
                
            except Exception as e:
                print(f"错误: 无法读取 {report_file}: {e}")
        
        print(f"\n数据加载完成:")
        print(f"- 模型数量: {len(self.models)}")
        print(f"- 代理数量: {len(self.agents)}")
        print(f"- 股票数量: {len(self.tickers)}")
        print(f"- 总数据点: {len(self.data)}")
    
    def create_comparison_dataframe(self, ticker: str) -> pd.DataFrame:
        """
        创建用于比较的DataFrame
        
        Args:
            ticker: 股票代码
            
        Returns:
            包含比较数据的DataFrame
        """
        # 筛选指定股票的数据
        filtered_data = {k: v for k, v in self.data.items() if k[1] == ticker}
        
        if not filtered_data:
            raise ValueError(f"未找到股票 {ticker} 的数据")
        
        # 创建DataFrame
        df_data = []
        for (model, _), agents_data in filtered_data.items():
            for agent, accuracy in agents_data.items():
                df_data.append({
                    'model': model,
                    'agent': agent,
                    'accuracy': accuracy
                })
        
        df = pd.DataFrame(df_data)
        
        # 透视表格式
        pivot_df = df.pivot(index='agent', columns='model', values='accuracy')
        pivot_df = pivot_df.fillna(0)  # 填充缺失值
        
        return pivot_df
    
    def plot_accuracy_comparison(self, ticker: str, save_path: Optional[str] = None, 
                               figsize: Tuple[int, int] = (15, 10)) -> None:
        """
        绘制准确率对比图
        
        Args:
            ticker: 股票代码
            save_path: 保存路径，如果为None则不保存
            figsize: 图表大小
        """
        df = self.create_comparison_dataframe(ticker)
        
        if df.empty:
            print(f"警告: 股票 {ticker} 没有可用数据")
            return
        
        # 创建图表
        fig, ax = plt.subplots(figsize=figsize)
        
        # 设置柱状图参数
        n_agents = len(df.index)
        n_models = len(df.columns)
        bar_width = 0.8 / n_models
        x = np.arange(n_agents)
        
        # 颜色方案
        colors = plt.colormaps['tab10'](np.linspace(0, 1, n_models))
        
        # 绘制分组柱状图
        for i, model in enumerate(df.columns):
            offset = (i - n_models/2 + 0.5) * bar_width
            bars = ax.bar(x + offset, df[model], bar_width, 
                         label=model, color=colors[i], alpha=0.8)
            
            # 在柱子上添加数值标签
            for bar in bars:
                height = bar.get_height()
                if height > 0:  # 只在有数据的柱子上显示标签
                    ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{height:.3f}', ha='center', va='bottom', fontsize=8)
        
        # 设置图表属性
        ax.set_xlabel('代理名称', fontsize=12, fontweight='bold')
        ax.set_ylabel('准确率', fontsize=12, fontweight='bold')
        ax.set_title(f'{ticker} 股票 - 不同LLM模型代理准确率对比', 
                    fontsize=16, fontweight='bold', pad=20)
        
        # 设置x轴标签
        agent_labels = [agent.replace('_agent', '').replace('_', ' ') for agent in df.index]
        ax.set_xticks(x)
        ax.set_xticklabels(agent_labels, rotation=45, ha='right')
        
        # 设置y轴范围
        ax.set_ylim(0, 1.0)
        ax.set_yticks(np.arange(0, 1.1, 0.1))
        
        # 添加网格
        ax.grid(True, alpha=0.3, axis='y')
        
        # 添加图例
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存到: {save_path}")

        # 关闭图表以释放内存
        plt.close()
    
    def generate_summary_report(self, ticker: str) -> str:
        """
        生成汇总报告
        
        Args:
            ticker: 股票代码
            
        Returns:
            汇总报告字符串
        """
        df = self.create_comparison_dataframe(ticker)
        
        if df.empty:
            return f"股票 {ticker} 没有可用数据"
        
        report = f"\n=== {ticker} 股票LLM模型代理准确率分析报告 ===\n\n"
        
        # 整体统计
        report += "1. 整体统计:\n"
        report += f"   - 分析的模型数量: {len(df.columns)}\n"
        report += f"   - 分析的代理数量: {len(df.index)}\n"
        report += f"   - 平均准确率: {df.mean().mean():.4f}\n\n"
        
        # 各模型平均表现
        report += "2. 各模型平均准确率:\n"
        model_avg = df.mean().sort_values(ascending=False)
        for model, avg_acc in model_avg.items():
            report += f"   - {model}: {avg_acc:.4f}\n"
        report += "\n"
        
        # 各代理平均表现
        report += "3. 各代理平均准确率:\n"
        agent_avg = df.mean(axis=1).sort_values(ascending=False)
        for agent, avg_acc in agent_avg.items():
            agent_name = str(agent).replace('_agent', '').replace('_', ' ')
            report += f"   - {agent_name}: {avg_acc:.4f}\n"
        report += "\n"
        
        # 最佳组合
        report += "4. 最佳模型-代理组合 (Top 5):\n"
        best_combinations = []
        for agent in df.index:
            for model in df.columns:
                try:
                    accuracy_value = df.loc[agent, model]
                    if pd.notna(accuracy_value) and accuracy_value > 0:
                        best_combinations.append((str(agent), str(model), accuracy_value))
                except:
                    continue

        best_combinations.sort(key=lambda x: x[2], reverse=True)
        for i, (agent, model, acc) in enumerate(best_combinations[:5]):
            agent_name = agent.replace('_agent', '').replace('_', ' ')
            report += f"   {i+1}. {agent_name} + {model}: {acc:.4f}\n"
        
        return report
    
    def save_data_to_csv(self, ticker: str, save_path: str) -> None:
        """
        保存数据到CSV文件
        
        Args:
            ticker: 股票代码
            save_path: 保存路径
        """
        df = self.create_comparison_dataframe(ticker)
        df.to_csv(save_path, encoding='utf-8-sig')
        print(f"数据已保存到CSV文件: {save_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='AI对冲基金系统LLM模型代理准确率分析工具')
    parser.add_argument('--ticker', '-t', type=str, default='AAPL',
                       help='目标股票代码 (默认: AAPL)')
    parser.add_argument('--base-path', '-p', type=str, default='reasoning_logs',
                       help='reasoning_logs文件夹路径 (默认: reasoning_logs)')
    parser.add_argument('--output-dir', '-o', type=str, default='accuracy_analysis_output',
                       help='输出文件夹路径 (默认: accuracy_analysis_output)')
    parser.add_argument('--figsize', type=str, default='15,10',
                       help='图表大小，格式: width,height (默认: 15,10)')
    parser.add_argument('--all-tickers', action='store_true',
                       help='分析所有可用的股票代码')
    parser.add_argument('--list-available', action='store_true',
                       help='列出所有可用的股票代码和模型')
    parser.add_argument('--date-range', type=str, default='20250101-20250601',
                       help='日期范围过滤，格式: YYYYMMDD-YYYYMMDD (默认: 20250101-20250601)')
    parser.add_argument('--no-date-filter', action='store_true',
                       help='不进行日期范围过滤，分析所有可用数据')

    args = parser.parse_args()
    
    # 解析图表大小
    try:
        figsize = tuple(map(int, args.figsize.split(',')))
    except:
        figsize = (15, 10)
        print("警告: 无法解析图表大小参数，使用默认值 (15, 10)")
    
    # 创建输出文件夹
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    try:
        # 初始化分析器
        date_range = None if args.no_date_filter else args.date_range
        analyzer = LLMAccuracyAnalyzer(args.base_path, date_range)

        # 如果用户要求列出可用数据
        if args.list_available:
            analyzer.load_accuracy_data()  # 加载所有数据
            print("\n=== 可用数据概览 ===")
            print(f"可用股票代码: {sorted(analyzer.tickers)}")
            print(f"可用模型: {sorted(analyzer.models)}")
            print(f"可用代理: {len(analyzer.agents)} 个")
            return

        # 加载数据
        if args.all_tickers:
            analyzer.load_accuracy_data()  # 加载所有股票数据
            tickers_to_analyze = sorted(analyzer.tickers)
        else:
            analyzer.load_accuracy_data(args.ticker)
            tickers_to_analyze = [args.ticker]

        if not analyzer.data:
            print(f"错误: 未找到数据")
            return
        
        # 分析每个股票
        for ticker in tickers_to_analyze:
            print(f"\n正在分析股票: {ticker}")

            # 检查该股票是否有数据
            ticker_data = {k: v for k, v in analyzer.data.items() if k[1] == ticker}
            if not ticker_data:
                print(f"警告: 股票 {ticker} 没有可用数据，跳过")
                continue

            # 生成可视化图表
            chart_path = output_dir / f"accuracy_comparison_{ticker}.png"
            analyzer.plot_accuracy_comparison(ticker, str(chart_path), figsize)

            # 生成汇总报告
            report = analyzer.generate_summary_report(ticker)
            print(report)

            # 保存报告到文件
            report_path = output_dir / f"accuracy_report_{ticker}.txt"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"报告已保存到: {report_path}")

            # 保存数据到CSV
            csv_path = output_dir / f"accuracy_data_{ticker}.csv"
            analyzer.save_data_to_csv(ticker, str(csv_path))

        print(f"\n分析完成! 所有输出文件已保存到: {output_dir}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
